﻿#pragma checksum "..\..\..\..\Controls\BytePositionSelector.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "1A467CA2AB61708E5D3123F1C9497F078CF41BEB"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AirMonitor.Controls {
    
    
    /// <summary>
    /// BytePositionSelector
    /// </summary>
    public partial class BytePositionSelector : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 100 "..\..\..\..\Controls\BytePositionSelector.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton Bit7;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\Controls\BytePositionSelector.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton Bit6;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\..\Controls\BytePositionSelector.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton Bit5;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\Controls\BytePositionSelector.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton Bit4;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\Controls\BytePositionSelector.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton Bit3;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\..\..\Controls\BytePositionSelector.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton Bit2;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\..\Controls\BytePositionSelector.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton Bit1;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\Controls\BytePositionSelector.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton Bit0;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AirMonitor;component/controls/bytepositionselector.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Controls\BytePositionSelector.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.Bit7 = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 100 "..\..\..\..\Controls\BytePositionSelector.xaml"
            this.Bit7.Click += new System.Windows.RoutedEventHandler(this.OnBitButtonClick);
            
            #line default
            #line hidden
            return;
            case 2:
            this.Bit6 = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 101 "..\..\..\..\Controls\BytePositionSelector.xaml"
            this.Bit6.Click += new System.Windows.RoutedEventHandler(this.OnBitButtonClick);
            
            #line default
            #line hidden
            return;
            case 3:
            this.Bit5 = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 102 "..\..\..\..\Controls\BytePositionSelector.xaml"
            this.Bit5.Click += new System.Windows.RoutedEventHandler(this.OnBitButtonClick);
            
            #line default
            #line hidden
            return;
            case 4:
            this.Bit4 = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 103 "..\..\..\..\Controls\BytePositionSelector.xaml"
            this.Bit4.Click += new System.Windows.RoutedEventHandler(this.OnBitButtonClick);
            
            #line default
            #line hidden
            return;
            case 5:
            this.Bit3 = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 104 "..\..\..\..\Controls\BytePositionSelector.xaml"
            this.Bit3.Click += new System.Windows.RoutedEventHandler(this.OnBitButtonClick);
            
            #line default
            #line hidden
            return;
            case 6:
            this.Bit2 = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 105 "..\..\..\..\Controls\BytePositionSelector.xaml"
            this.Bit2.Click += new System.Windows.RoutedEventHandler(this.OnBitButtonClick);
            
            #line default
            #line hidden
            return;
            case 7:
            this.Bit1 = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 106 "..\..\..\..\Controls\BytePositionSelector.xaml"
            this.Bit1.Click += new System.Windows.RoutedEventHandler(this.OnBitButtonClick);
            
            #line default
            #line hidden
            return;
            case 8:
            this.Bit0 = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 107 "..\..\..\..\Controls\BytePositionSelector.xaml"
            this.Bit0.Click += new System.Windows.RoutedEventHandler(this.OnBitButtonClick);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

