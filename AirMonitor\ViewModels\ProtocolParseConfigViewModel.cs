using AirMonitor.Models;
using AirMonitor.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;
using System.Windows;
using System.Windows.Input;

namespace AirMonitor.ViewModels;

/// <summary>
/// 协议数据解析配置界面ViewModel
/// </summary>
public partial class ProtocolParseConfigViewModel : ViewModelBase
{
    #region 字段

    private readonly IProtocolParseConfigService _configService;
    private readonly IDialogService _dialogService;

    #endregion

    #region 属性

    /// <summary>
    /// 协议解析配置列表
    /// </summary>
    public ObservableCollection<ProtocolParseConfig> Configs { get; } = new();

    /// <summary>
    /// 物理量定义列表
    /// </summary>
    public ObservableCollection<PhysicalQuantity> PhysicalQuantities { get; } = new();

    /// <summary>
    /// 物理量分组列表
    /// </summary>
    public ObservableCollection<PhysicalQuantityGroup> PhysicalQuantityGroups { get; } = new();

    /// <summary>
    /// 数据映射配置列表
    /// </summary>
    public ObservableCollection<DataMappingConfig> DataMappings { get; } = new();

    /// <summary>
    /// 协议类型选项
    /// </summary>
    public ObservableCollection<DataPacketProtocolType> ProtocolTypes { get; } = new()
    {
        DataPacketProtocolType.CommercialProtocol,
        DataPacketProtocolType.ModuleProtocol
    };

    /// <summary>
    /// 数据映射类型选项
    /// </summary>
    public ObservableCollection<DataMappingType> MappingTypes { get; } = new()
    {
        DataMappingType.ByteMapping,
        DataMappingType.BitMapping,
        DataMappingType.CrossByteBitMapping,
        DataMappingType.IndexMapping
    };

    /// <summary>
    /// 数据类型选项
    /// </summary>
    public ObservableCollection<DataValueType> DataTypes { get; } = new()
    {
        DataValueType.UInt8,
        DataValueType.Int8,
        DataValueType.UInt16,
        DataValueType.Int16,
        DataValueType.UInt32,
        DataValueType.Int32,
        DataValueType.Float,
        DataValueType.Double,
        DataValueType.Boolean,
        DataValueType.String
    };

    /// <summary>
    /// 字节序选项
    /// </summary>
    public ObservableCollection<ByteOrder> ByteOrders { get; } = new()
    {
        ByteOrder.LittleEndian,
        ByteOrder.BigEndian
    };

    [ObservableProperty]
    private ProtocolParseConfig? _selectedConfig;

    [ObservableProperty]
    private PhysicalQuantity? _selectedPhysicalQuantity;

    [ObservableProperty]
    private DataMappingConfig? _selectedDataMapping;

    [ObservableProperty]
    private bool _isConfigEditing = false;

    [ObservableProperty]
    private bool _isPhysicalQuantityEditing = false;

    [ObservableProperty]
    private bool _isDataMappingEditing = false;

    [ObservableProperty]
    private string _statusMessage = "就绪";

    [ObservableProperty]
    private bool _isBusy = false;

    [ObservableProperty]
    private string _searchText = string.Empty;

    [ObservableProperty]
    private DataPacketProtocolType _filterProtocolType = DataPacketProtocolType.CommercialProtocol;

    #endregion

    #region 命令

    /// <summary>
    /// 新建配置命令
    /// </summary>
    public ICommand NewConfigCommand { get; }

    /// <summary>
    /// 编辑配置命令
    /// </summary>
    public ICommand EditConfigCommand { get; }

    /// <summary>
    /// 删除配置命令
    /// </summary>
    public ICommand DeleteConfigCommand { get; }

    /// <summary>
    /// 复制配置命令
    /// </summary>
    public ICommand CopyConfigCommand { get; }

    /// <summary>
    /// 保存配置命令
    /// </summary>
    public ICommand SaveConfigCommand { get; }

    /// <summary>
    /// 取消编辑命令
    /// </summary>
    public ICommand CancelEditCommand { get; }

    /// <summary>
    /// 新建物理量命令
    /// </summary>
    public ICommand NewPhysicalQuantityCommand { get; }

    /// <summary>
    /// 编辑物理量命令
    /// </summary>
    public ICommand EditPhysicalQuantityCommand { get; }

    /// <summary>
    /// 删除物理量命令
    /// </summary>
    public ICommand DeletePhysicalQuantityCommand { get; }

    /// <summary>
    /// 保存物理量命令
    /// </summary>
    public ICommand SavePhysicalQuantityCommand { get; }

    /// <summary>
    /// 新建数据映射命令
    /// </summary>
    public ICommand NewDataMappingCommand { get; }

    /// <summary>
    /// 编辑数据映射命令
    /// </summary>
    public ICommand EditDataMappingCommand { get; }

    /// <summary>
    /// 删除数据映射命令
    /// </summary>
    public ICommand DeleteDataMappingCommand { get; }

    /// <summary>
    /// 保存数据映射命令
    /// </summary>
    public ICommand SaveDataMappingCommand { get; }

    /// <summary>
    /// 导入配置命令
    /// </summary>
    public ICommand ImportConfigCommand { get; }

    /// <summary>
    /// 导出配置命令
    /// </summary>
    public ICommand ExportConfigCommand { get; }

    /// <summary>
    /// 刷新命令
    /// </summary>
    public ICommand RefreshCommand { get; }

    /// <summary>
    /// 搜索命令
    /// </summary>
    public ICommand SearchCommand { get; }

    /// <summary>
    /// 验证配置命令
    /// </summary>
    public ICommand ValidateConfigCommand { get; }

    #endregion

    #region 构造函数

    public ProtocolParseConfigViewModel(
        IProtocolParseConfigService configService,
        IDialogService dialogService,
        ILoggingService loggingService) : base(loggingService)
    {
        _configService = configService ?? throw new ArgumentNullException(nameof(configService));
        _dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));

        Title = "协议数据解析配置";

        // 初始化命令
        NewConfigCommand = new AsyncRelayCommand(NewConfigAsync);
        EditConfigCommand = new AsyncRelayCommand(EditConfigAsync, () => SelectedConfig != null);
        DeleteConfigCommand = new AsyncRelayCommand(DeleteConfigAsync, () => SelectedConfig != null);
        CopyConfigCommand = new AsyncRelayCommand(CopyConfigAsync, () => SelectedConfig != null);
        SaveConfigCommand = new AsyncRelayCommand(SaveConfigAsync, () => IsConfigEditing);
        CancelEditCommand = new RelayCommand(CancelEdit, () => IsConfigEditing || IsPhysicalQuantityEditing || IsDataMappingEditing);

        NewPhysicalQuantityCommand = new AsyncRelayCommand(NewPhysicalQuantityAsync);
        EditPhysicalQuantityCommand = new AsyncRelayCommand(EditPhysicalQuantityAsync, () => SelectedPhysicalQuantity != null);
        DeletePhysicalQuantityCommand = new AsyncRelayCommand(DeletePhysicalQuantityAsync, () => SelectedPhysicalQuantity != null);
        SavePhysicalQuantityCommand = new AsyncRelayCommand(SavePhysicalQuantityAsync, () => IsPhysicalQuantityEditing);

        NewDataMappingCommand = new AsyncRelayCommand(NewDataMappingAsync, () => SelectedConfig != null);
        EditDataMappingCommand = new AsyncRelayCommand(EditDataMappingAsync, () => SelectedDataMapping != null);
        DeleteDataMappingCommand = new AsyncRelayCommand(DeleteDataMappingAsync, () => SelectedDataMapping != null);
        SaveDataMappingCommand = new AsyncRelayCommand(SaveDataMappingAsync, () => IsDataMappingEditing);

        ImportConfigCommand = new AsyncRelayCommand(ImportConfigAsync);
        ExportConfigCommand = new AsyncRelayCommand(ExportConfigAsync, () => SelectedConfig != null);
        RefreshCommand = new AsyncRelayCommand(RefreshAsync);
        SearchCommand = new AsyncRelayCommand(SearchAsync);
        ValidateConfigCommand = new AsyncRelayCommand(ValidateConfigAsync, () => SelectedConfig != null);

        // 订阅属性变化事件
        PropertyChanged += OnPropertyChanged;

        // 订阅配置服务事件
        _configService.ConfigChanged += OnConfigChanged;
        _configService.PhysicalQuantityChanged += OnPhysicalQuantityChanged;
    }

    #endregion

    #region 初始化

    protected override async Task OnInitializeAsync()
    {
        try
        {
            IsBusy = true;
            StatusMessage = "正在加载配置数据...";

            await LoadConfigsAsync();
            await LoadPhysicalQuantitiesAsync();

            StatusMessage = "配置数据加载完成";
            LoggingService?.LogInformation("协议数据解析配置界面初始化完成");
        }
        catch (Exception ex)
        {
            await OnErrorAsync(ex, "初始化协议数据解析配置界面");
        }
        finally
        {
            IsBusy = false;
        }
    }

    #endregion

    #region 私有方法

    private async Task LoadConfigsAsync()
    {
        try
        {
            var configs = await _configService.GetAllConfigsAsync();
            
            Configs.Clear();
            foreach (var config in configs.OrderBy(c => c.Name))
            {
                Configs.Add(config);
            }

            LoggingService?.LogDebug("加载了 {Count} 个协议解析配置", configs.Count);
        }
        catch (Exception ex)
        {
            LoggingService?.LogError(ex, "加载协议解析配置时发生错误");
            throw;
        }
    }

    private async Task LoadPhysicalQuantitiesAsync()
    {
        try
        {
            var quantities = await _configService.GetAllPhysicalQuantitiesAsync();
            var groups = await _configService.GetPhysicalQuantityGroupsAsync();
            
            PhysicalQuantities.Clear();
            foreach (var quantity in quantities.OrderBy(q => q.GroupName).ThenBy(q => q.SortOrder).ThenBy(q => q.Name))
            {
                PhysicalQuantities.Add(quantity);
            }

            PhysicalQuantityGroups.Clear();
            foreach (var group in groups.OrderBy(g => g.SortOrder).ThenBy(g => g.Name))
            {
                PhysicalQuantityGroups.Add(group);
            }

            LoggingService?.LogDebug("加载了 {QuantityCount} 个物理量定义，{GroupCount} 个分组", quantities.Count, groups.Count);
        }
        catch (Exception ex)
        {
            LoggingService?.LogError(ex, "加载物理量定义时发生错误");
            throw;
        }
    }

    private void OnPropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        // 当选择的配置变化时，更新数据映射列表
        if (e.PropertyName == nameof(SelectedConfig))
        {
            UpdateDataMappingsList();
            UpdateCommandCanExecute();
        }
        else if (e.PropertyName == nameof(SelectedPhysicalQuantity) || 
                 e.PropertyName == nameof(SelectedDataMapping))
        {
            UpdateCommandCanExecute();
        }
        else if (e.PropertyName == nameof(IsConfigEditing) || 
                 e.PropertyName == nameof(IsPhysicalQuantityEditing) || 
                 e.PropertyName == nameof(IsDataMappingEditing))
        {
            UpdateCommandCanExecute();
        }
    }

    private void UpdateDataMappingsList()
    {
        DataMappings.Clear();
        if (SelectedConfig != null)
        {
            foreach (var mapping in SelectedConfig.DataMappings.OrderBy(m => m.SortOrder).ThenBy(m => m.Name))
            {
                DataMappings.Add(mapping);
            }
        }
    }

    private void UpdateCommandCanExecute()
    {
        // 通知所有命令重新评估CanExecute状态
        ((AsyncRelayCommand)EditConfigCommand).NotifyCanExecuteChanged();
        ((AsyncRelayCommand)DeleteConfigCommand).NotifyCanExecuteChanged();
        ((AsyncRelayCommand)CopyConfigCommand).NotifyCanExecuteChanged();
        ((AsyncRelayCommand)SaveConfigCommand).NotifyCanExecuteChanged();
        ((RelayCommand)CancelEditCommand).NotifyCanExecuteChanged();
        
        ((AsyncRelayCommand)EditPhysicalQuantityCommand).NotifyCanExecuteChanged();
        ((AsyncRelayCommand)DeletePhysicalQuantityCommand).NotifyCanExecuteChanged();
        ((AsyncRelayCommand)SavePhysicalQuantityCommand).NotifyCanExecuteChanged();
        
        ((AsyncRelayCommand)NewDataMappingCommand).NotifyCanExecuteChanged();
        ((AsyncRelayCommand)EditDataMappingCommand).NotifyCanExecuteChanged();
        ((AsyncRelayCommand)DeleteDataMappingCommand).NotifyCanExecuteChanged();
        ((AsyncRelayCommand)SaveDataMappingCommand).NotifyCanExecuteChanged();
        
        ((AsyncRelayCommand)ExportConfigCommand).NotifyCanExecuteChanged();
        ((AsyncRelayCommand)ValidateConfigCommand).NotifyCanExecuteChanged();
    }

    #endregion

    #region 命令实现

    private async Task NewConfigAsync()
    {
        try
        {
            var newConfig = new ProtocolParseConfig
            {
                Name = "新配置",
                Description = "新建的协议解析配置",
                ProtocolType = FilterProtocolType,
                Version = "1.0.0"
            };

            SelectedConfig = newConfig;
            IsConfigEditing = true;
            StatusMessage = "正在创建新配置...";

            LoggingService?.LogInformation("开始创建新的协议解析配置");
        }
        catch (Exception ex)
        {
            await OnErrorAsync(ex, "创建新配置");
        }
    }

    private async Task EditConfigAsync()
    {
        if (SelectedConfig == null) return;

        try
        {
            IsConfigEditing = true;
            StatusMessage = $"正在编辑配置: {SelectedConfig.Name}";

            LoggingService?.LogInformation("开始编辑协议解析配置: {ConfigName}", SelectedConfig.Name);
        }
        catch (Exception ex)
        {
            await OnErrorAsync(ex, "编辑配置");
        }
    }

    private async Task DeleteConfigAsync()
    {
        if (SelectedConfig == null) return;

        try
        {
            // TODO: 显示确认对话框
            // var result = await _dialogService.ShowConfirmationAsync(
            //     "确认删除",
            //     $"确定要删除配置 '{SelectedConfig.Name}' 吗？此操作不可撤销。");
            // if (result != true) return;

            var configName = SelectedConfig.Name;
            var success = await _configService.DeleteConfigAsync(SelectedConfig.Id);

            if (success)
            {
                Configs.Remove(SelectedConfig);
                SelectedConfig = null;
                StatusMessage = $"配置 '{configName}' 已删除";
                LoggingService?.LogInformation("协议解析配置删除成功: {ConfigName}", configName);
            }
            else
            {
                StatusMessage = $"删除配置 '{configName}' 失败";
                LoggingService?.LogWarning("协议解析配置删除失败: {ConfigName}", configName);
            }
        }
        catch (Exception ex)
        {
            await OnErrorAsync(ex, "删除配置");
        }
    }

    private async Task CopyConfigAsync()
    {
        if (SelectedConfig == null) return;

        try
        {
            var newName = $"{SelectedConfig.Name} - 副本";
            var copiedConfig = await _configService.CopyConfigAsync(SelectedConfig.Id, newName);

            if (copiedConfig != null)
            {
                Configs.Add(copiedConfig);
                SelectedConfig = copiedConfig;
                StatusMessage = $"配置已复制为: {newName}";
                LoggingService?.LogInformation("协议解析配置复制成功: {OriginalName} -> {NewName}",
                    SelectedConfig.Name, newName);
            }
            else
            {
                StatusMessage = "复制配置失败";
                LoggingService?.LogWarning("协议解析配置复制失败: {ConfigName}", SelectedConfig.Name);
            }
        }
        catch (Exception ex)
        {
            await OnErrorAsync(ex, "复制配置");
        }
    }

    private async Task SaveConfigAsync()
    {
        if (SelectedConfig == null) return;

        try
        {
            IsBusy = true;
            StatusMessage = "正在保存配置...";

            var success = await _configService.SaveConfigAsync(SelectedConfig);

            if (success)
            {
                IsConfigEditing = false;

                // 如果是新配置，添加到列表中
                if (!Configs.Contains(SelectedConfig))
                {
                    Configs.Add(SelectedConfig);
                }

                StatusMessage = $"配置 '{SelectedConfig.Name}' 保存成功";
                LoggingService?.LogInformation("协议解析配置保存成功: {ConfigName}", SelectedConfig.Name);
            }
            else
            {
                StatusMessage = $"保存配置 '{SelectedConfig.Name}' 失败";
                LoggingService?.LogWarning("协议解析配置保存失败: {ConfigName}", SelectedConfig.Name);
            }
        }
        catch (Exception ex)
        {
            await OnErrorAsync(ex, "保存配置");
        }
        finally
        {
            IsBusy = false;
        }
    }

    private void CancelEdit()
    {
        try
        {
            if (IsConfigEditing)
            {
                IsConfigEditing = false;
                // 如果是新配置且未保存，清除选择
                if (SelectedConfig != null && !Configs.Contains(SelectedConfig))
                {
                    SelectedConfig = null;
                }
            }

            if (IsPhysicalQuantityEditing)
            {
                IsPhysicalQuantityEditing = false;
                // 如果是新物理量且未保存，清除选择
                if (SelectedPhysicalQuantity != null && !PhysicalQuantities.Contains(SelectedPhysicalQuantity))
                {
                    SelectedPhysicalQuantity = null;
                }
            }

            if (IsDataMappingEditing)
            {
                IsDataMappingEditing = false;
                // 如果是新数据映射且未保存，清除选择
                if (SelectedDataMapping != null && !DataMappings.Contains(SelectedDataMapping))
                {
                    SelectedDataMapping = null;
                }
            }

            StatusMessage = "已取消编辑";
            LoggingService?.LogInformation("用户取消了编辑操作");
        }
        catch (Exception ex)
        {
            LoggingService?.LogError(ex, "取消编辑时发生错误");
        }
    }

    private async Task NewPhysicalQuantityAsync()
    {
        try
        {
            var newQuantity = new PhysicalQuantity
            {
                Name = "新物理量",
                DisplayName = "新物理量",
                Description = "新建的物理量定义",
                DataType = DataValueType.UInt8,
                Unit = "",
                GroupName = "默认分组"
            };

            SelectedPhysicalQuantity = newQuantity;
            IsPhysicalQuantityEditing = true;
            StatusMessage = "正在创建新物理量...";

            LoggingService?.LogInformation("开始创建新的物理量定义");
        }
        catch (Exception ex)
        {
            await OnErrorAsync(ex, "创建新物理量");
        }
    }

    private async Task EditPhysicalQuantityAsync()
    {
        if (SelectedPhysicalQuantity == null) return;

        try
        {
            IsPhysicalQuantityEditing = true;
            StatusMessage = $"正在编辑物理量: {SelectedPhysicalQuantity.DisplayName}";

            LoggingService?.LogInformation("开始编辑物理量定义: {QuantityName}", SelectedPhysicalQuantity.Name);
        }
        catch (Exception ex)
        {
            await OnErrorAsync(ex, "编辑物理量");
        }
    }

    private async Task DeletePhysicalQuantityAsync()
    {
        if (SelectedPhysicalQuantity == null) return;

        try
        {
            // TODO: 显示确认对话框并检查是否被配置引用
            var quantityName = SelectedPhysicalQuantity.DisplayName;
            var success = await _configService.DeletePhysicalQuantityAsync(SelectedPhysicalQuantity.Name);

            if (success)
            {
                PhysicalQuantities.Remove(SelectedPhysicalQuantity);
                SelectedPhysicalQuantity = null;
                StatusMessage = $"物理量 '{quantityName}' 已删除";
                LoggingService?.LogInformation("物理量定义删除成功: {QuantityName}", quantityName);
            }
            else
            {
                StatusMessage = $"删除物理量 '{quantityName}' 失败";
                LoggingService?.LogWarning("物理量定义删除失败: {QuantityName}", quantityName);
            }
        }
        catch (Exception ex)
        {
            await OnErrorAsync(ex, "删除物理量");
        }
    }

    private async Task SavePhysicalQuantityAsync()
    {
        if (SelectedPhysicalQuantity == null) return;

        try
        {
            IsBusy = true;
            StatusMessage = "正在保存物理量...";

            var success = await _configService.SavePhysicalQuantityAsync(SelectedPhysicalQuantity);

            if (success)
            {
                IsPhysicalQuantityEditing = false;

                // 如果是新物理量，添加到列表中
                if (!PhysicalQuantities.Contains(SelectedPhysicalQuantity))
                {
                    PhysicalQuantities.Add(SelectedPhysicalQuantity);
                }

                // 刷新分组列表
                await LoadPhysicalQuantitiesAsync();

                StatusMessage = $"物理量 '{SelectedPhysicalQuantity.DisplayName}' 保存成功";
                LoggingService?.LogInformation("物理量定义保存成功: {QuantityName}", SelectedPhysicalQuantity.Name);
            }
            else
            {
                StatusMessage = $"保存物理量 '{SelectedPhysicalQuantity.DisplayName}' 失败";
                LoggingService?.LogWarning("物理量定义保存失败: {QuantityName}", SelectedPhysicalQuantity.Name);
            }
        }
        catch (Exception ex)
        {
            await OnErrorAsync(ex, "保存物理量");
        }
        finally
        {
            IsBusy = false;
        }
    }

    #endregion

    #region 事件处理

    private void OnConfigChanged(object? sender, ProtocolParseConfigChangedEventArgs e)
    {
        // 在UI线程中更新界面
        Application.Current?.Dispatcher.BeginInvoke(() =>
        {
            try
            {
                switch (e.ChangeType)
                {
                    case ConfigChangeType.Added:
                        if (!Configs.Contains(e.Config))
                        {
                            Configs.Add(e.Config);
                        }
                        break;

                    case ConfigChangeType.Updated:
                        var existingConfig = Configs.FirstOrDefault(c => c.Id == e.Config.Id);
                        if (existingConfig != null)
                        {
                            var index = Configs.IndexOf(existingConfig);
                            Configs[index] = e.Config;
                        }
                        break;

                    case ConfigChangeType.Deleted:
                        var configToRemove = Configs.FirstOrDefault(c => c.Id == e.Config.Id);
                        if (configToRemove != null)
                        {
                            Configs.Remove(configToRemove);
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                LoggingService?.LogError(ex, "处理配置变更事件时发生错误");
            }
        });
    }

    private void OnPhysicalQuantityChanged(object? sender, PhysicalQuantityChangedEventArgs e)
    {
        // 在UI线程中更新界面
        Application.Current?.Dispatcher.BeginInvoke(() =>
        {
            try
            {
                switch (e.ChangeType)
                {
                    case ConfigChangeType.Added:
                        if (!PhysicalQuantities.Contains(e.PhysicalQuantity))
                        {
                            PhysicalQuantities.Add(e.PhysicalQuantity);
                        }
                        break;

                    case ConfigChangeType.Updated:
                        var existingQuantity = PhysicalQuantities.FirstOrDefault(q => q.Name == e.PhysicalQuantity.Name);
                        if (existingQuantity != null)
                        {
                            var index = PhysicalQuantities.IndexOf(existingQuantity);
                            PhysicalQuantities[index] = e.PhysicalQuantity;
                        }
                        break;

                    case ConfigChangeType.Deleted:
                        var quantityToRemove = PhysicalQuantities.FirstOrDefault(q => q.Name == e.PhysicalQuantity.Name);
                        if (quantityToRemove != null)
                        {
                            PhysicalQuantities.Remove(quantityToRemove);
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                LoggingService?.LogError(ex, "处理物理量变更事件时发生错误");
            }
        });
    }

    #endregion

    #region 数据映射命令实现

    private async Task NewDataMappingAsync()
    {
        if (SelectedConfig == null) return;

        try
        {
            var newMapping = new DataMappingConfig
            {
                Name = "新数据映射",
                Description = "新建的数据映射配置",
                MappingType = DataMappingType.ByteMapping,
                PhysicalQuantityName = PhysicalQuantities.FirstOrDefault()?.Name ?? ""
            };

            SelectedDataMapping = newMapping;
            IsDataMappingEditing = true;
            StatusMessage = "正在创建新数据映射...";

            LoggingService?.LogInformation("开始创建新的数据映射配置");
        }
        catch (Exception ex)
        {
            await OnErrorAsync(ex, "创建新数据映射");
        }
    }

    private async Task EditDataMappingAsync()
    {
        if (SelectedDataMapping == null) return;

        try
        {
            IsDataMappingEditing = true;
            StatusMessage = $"正在编辑数据映射: {SelectedDataMapping.Name}";

            LoggingService?.LogInformation("开始编辑数据映射配置: {MappingName}", SelectedDataMapping.Name);
        }
        catch (Exception ex)
        {
            await OnErrorAsync(ex, "编辑数据映射");
        }
    }

    private async Task DeleteDataMappingAsync()
    {
        if (SelectedDataMapping == null || SelectedConfig == null) return;

        try
        {
            var mappingName = SelectedDataMapping.Name;
            SelectedConfig.DataMappings.Remove(SelectedDataMapping);
            DataMappings.Remove(SelectedDataMapping);
            SelectedDataMapping = null;

            StatusMessage = $"数据映射 '{mappingName}' 已删除";
            LoggingService?.LogInformation("数据映射配置删除成功: {MappingName}", mappingName);
        }
        catch (Exception ex)
        {
            await OnErrorAsync(ex, "删除数据映射");
        }
    }

    private async Task SaveDataMappingAsync()
    {
        if (SelectedDataMapping == null || SelectedConfig == null) return;

        try
        {
            IsBusy = true;
            StatusMessage = "正在保存数据映射...";

            // 验证数据映射配置
            var validation = SelectedDataMapping.ValidateConfig();
            if (!validation.IsValid)
            {
                StatusMessage = $"数据映射配置验证失败: {string.Join("; ", validation.ErrorMessages)}";
                return;
            }

            IsDataMappingEditing = false;

            // 如果是新数据映射，添加到配置中
            if (!SelectedConfig.DataMappings.Contains(SelectedDataMapping))
            {
                SelectedConfig.DataMappings.Add(SelectedDataMapping);
                DataMappings.Add(SelectedDataMapping);
            }

            StatusMessage = $"数据映射 '{SelectedDataMapping.Name}' 保存成功";
            LoggingService?.LogInformation("数据映射配置保存成功: {MappingName}", SelectedDataMapping.Name);
        }
        catch (Exception ex)
        {
            await OnErrorAsync(ex, "保存数据映射");
        }
        finally
        {
            IsBusy = false;
        }
    }

    #endregion

    #region 其他命令实现

    private async Task ImportConfigAsync()
    {
        try
        {
            // TODO: 显示文件选择对话框
            // var filePath = await _dialogService.ShowOpenFileDialogAsync(
            //     "导入配置",
            //     "JSON文件|*.json|所有文件|*.*");
            // if (string.IsNullOrEmpty(filePath)) return;

            StatusMessage = "导入配置功能待实现";
            LoggingService?.LogInformation("用户尝试导入配置");
        }
        catch (Exception ex)
        {
            await OnErrorAsync(ex, "导入配置");
        }
    }

    private async Task ExportConfigAsync()
    {
        if (SelectedConfig == null) return;

        try
        {
            // TODO: 显示文件保存对话框
            // var filePath = await _dialogService.ShowSaveFileDialogAsync(
            //     "导出配置",
            //     $"{SelectedConfig.Name}.json",
            //     "JSON文件|*.json|所有文件|*.*");
            // if (string.IsNullOrEmpty(filePath)) return;

            StatusMessage = "导出配置功能待实现";
            LoggingService?.LogInformation("用户尝试导出配置: {ConfigName}", SelectedConfig.Name);
        }
        catch (Exception ex)
        {
            await OnErrorAsync(ex, "导出配置");
        }
    }

    private async Task RefreshAsync()
    {
        try
        {
            IsBusy = true;
            StatusMessage = "正在刷新数据...";

            await LoadConfigsAsync();
            await LoadPhysicalQuantitiesAsync();

            StatusMessage = "数据刷新完成";
            LoggingService?.LogInformation("协议数据解析配置数据已刷新");
        }
        catch (Exception ex)
        {
            await OnErrorAsync(ex, "刷新数据");
        }
        finally
        {
            IsBusy = false;
        }
    }

    private async Task SearchAsync()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(SearchText))
            {
                await LoadConfigsAsync();
                return;
            }

            var allConfigs = await _configService.GetAllConfigsAsync();
            var filteredConfigs = allConfigs.Where(c =>
                c.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                c.Description.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                c.ProtocolType.ToString().Contains(SearchText, StringComparison.OrdinalIgnoreCase))
                .OrderBy(c => c.Name);

            Configs.Clear();
            foreach (var config in filteredConfigs)
            {
                Configs.Add(config);
            }

            StatusMessage = $"搜索完成，找到 {Configs.Count} 个匹配的配置";
            LoggingService?.LogInformation("搜索配置完成: 关键词='{SearchText}', 结果数量={Count}", SearchText, Configs.Count);
        }
        catch (Exception ex)
        {
            await OnErrorAsync(ex, "搜索配置");
        }
    }

    private async Task ValidateConfigAsync()
    {
        if (SelectedConfig == null) return;

        try
        {
            IsBusy = true;
            StatusMessage = "正在验证配置...";

            var validation = await _configService.ValidateConfigAsync(SelectedConfig);

            if (validation.IsValid)
            {
                StatusMessage = $"配置 '{SelectedConfig.Name}' 验证通过";
                LoggingService?.LogInformation("配置验证通过: {ConfigName}", SelectedConfig.Name);
            }
            else
            {
                var errorMessage = string.Join("; ", validation.ErrorMessages);
                StatusMessage = $"配置验证失败: {errorMessage}";
                LoggingService?.LogWarning("配置验证失败: {ConfigName}, 错误: {Errors}",
                    SelectedConfig.Name, errorMessage);

                // TODO: 显示详细的验证结果对话框
            }
        }
        catch (Exception ex)
        {
            await OnErrorAsync(ex, "验证配置");
        }
        finally
        {
            IsBusy = false;
        }
    }

    #endregion

    #region 清理

    protected override void OnDispose()
    {
        try
        {
            // 取消订阅事件
            if (_configService != null)
            {
                _configService.ConfigChanged -= OnConfigChanged;
                _configService.PhysicalQuantityChanged -= OnPhysicalQuantityChanged;
            }

            LoggingService?.LogInformation("协议数据解析配置ViewModel已释放");
        }
        catch (Exception ex)
        {
            LoggingService?.LogError(ex, "释放协议数据解析配置ViewModel时发生错误");
        }

        base.OnDispose();
    }

    #endregion
}
