using System.Globalization;
using System.Windows.Data;
using System.Windows;
using AirMonitor.Models;
using System.ComponentModel;

namespace AirMonitor.Converters;

/// <summary>
/// 数据映射类型到描述的转换器
/// </summary>
[ValueConversion(typeof(DataMappingType), typeof(string))]
public class DataMappingTypeToDescriptionConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is DataMappingType mappingType)
        {
            var field = mappingType.GetType().GetField(mappingType.ToString());
            if (field != null)
            {
                var attribute = field.GetCustomAttributes(typeof(DescriptionAttribute), false)
                    .FirstOrDefault() as DescriptionAttribute;
                return attribute?.Description ?? mappingType.ToString();
            }
        }
        return value?.ToString() ?? string.Empty;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 数据类型到描述的转换器
/// </summary>
[ValueConversion(typeof(DataValueType), typeof(string))]
public class DataValueTypeToDescriptionConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is DataValueType dataType)
        {
            var field = dataType.GetType().GetField(dataType.ToString());
            if (field != null)
            {
                var attribute = field.GetCustomAttributes(typeof(DescriptionAttribute), false)
                    .FirstOrDefault() as DescriptionAttribute;
                return attribute?.Description ?? dataType.ToString();
            }
        }
        return value?.ToString() ?? string.Empty;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 协议类型到描述的转换器
/// </summary>
[ValueConversion(typeof(DataPacketProtocolType), typeof(string))]
public class ProtocolTypeToDescriptionConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is DataPacketProtocolType protocolType)
        {
            var field = protocolType.GetType().GetField(protocolType.ToString());
            if (field != null)
            {
                var attribute = field.GetCustomAttributes(typeof(DescriptionAttribute), false)
                    .FirstOrDefault() as DescriptionAttribute;
                return attribute?.Description ?? protocolType.ToString();
            }
        }
        return value?.ToString() ?? string.Empty;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 字节序到描述的转换器
/// </summary>
[ValueConversion(typeof(ByteOrder), typeof(string))]
public class ByteOrderToDescriptionConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is ByteOrder byteOrder)
        {
            var field = byteOrder.GetType().GetField(byteOrder.ToString());
            if (field != null)
            {
                var attribute = field.GetCustomAttributes(typeof(DescriptionAttribute), false)
                    .FirstOrDefault() as DescriptionAttribute;
                return attribute?.Description ?? byteOrder.ToString();
            }
        }
        return value?.ToString() ?? string.Empty;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 布尔值到可见性的转换器（反向）
/// </summary>
[ValueConversion(typeof(bool), typeof(Visibility))]
public class InverseBooleanToVisibilityConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool boolValue)
        {
            return boolValue ? Visibility.Collapsed : Visibility.Visible;
        }
        return Visibility.Visible;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is Visibility visibility)
        {
            return visibility != Visibility.Visible;
        }
        return false;
    }
}

/// <summary>
/// 数值到十六进制字符串的转换器
/// </summary>
[ValueConversion(typeof(object), typeof(string))]
public class NumberToHexStringConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value == null) return string.Empty;

        try
        {
            if (value is byte byteValue)
                return $"0x{byteValue:X2}";
            else if (value is ushort ushortValue)
                return $"0x{ushortValue:X4}";
            else if (value is uint uintValue)
                return $"0x{uintValue:X8}";
            else if (value is int intValue && intValue >= 0)
                return $"0x{intValue:X}";
            else
                return value.ToString() ?? string.Empty;
        }
        catch
        {
            return value.ToString() ?? string.Empty;
        }
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is string hexString && !string.IsNullOrWhiteSpace(hexString))
        {
            try
            {
                // 移除0x前缀
                if (hexString.StartsWith("0x", StringComparison.OrdinalIgnoreCase))
                {
                    hexString = hexString.Substring(2);
                }

                if (targetType == typeof(byte) || targetType == typeof(byte?))
                {
                    return System.Convert.ToByte(hexString, 16);
                }
                else if (targetType == typeof(ushort) || targetType == typeof(ushort?))
                {
                    return System.Convert.ToUInt16(hexString, 16);
                }
                else if (targetType == typeof(uint) || targetType == typeof(uint?))
                {
                    return System.Convert.ToUInt32(hexString, 16);
                }
                else if (targetType == typeof(int) || targetType == typeof(int?))
                {
                    return System.Convert.ToInt32(hexString, 16);
                }
            }
            catch
            {
                // 转换失败，返回默认值
            }
        }

        return Activator.CreateInstance(targetType);
    }
}

/// <summary>
/// 验证结果到颜色的转换器
/// </summary>
[ValueConversion(typeof(ValidationResult), typeof(System.Windows.Media.Brush))]
public class ValidationResultToBrushConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is ValidationResult validation)
        {
            if (validation.IsValid)
            {
                return System.Windows.Media.Brushes.Green;
            }
            else
            {
                return System.Windows.Media.Brushes.Red;
            }
        }
        return System.Windows.Media.Brushes.Gray;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 集合计数到可见性的转换器
/// </summary>
[ValueConversion(typeof(int), typeof(Visibility))]
public class CountToVisibilityConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is int count)
        {
            bool showWhenEmpty = parameter?.ToString()?.ToLower() == "true";
            
            if (showWhenEmpty)
            {
                return count == 0 ? Visibility.Visible : Visibility.Collapsed;
            }
            else
            {
                return count > 0 ? Visibility.Visible : Visibility.Collapsed;
            }
        }
        return Visibility.Collapsed;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 位位置到字符串的转换器
/// </summary>
[ValueConversion(typeof(BitPosition), typeof(string))]
public class BitPositionToStringConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is BitPosition bitPosition)
        {
            return bitPosition.ToString();
        }
        return string.Empty;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 字节范围到字符串的转换器
/// </summary>
[ValueConversion(typeof(ByteRange), typeof(string))]
public class ByteRangeToStringConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is ByteRange byteRange)
        {
            return byteRange.ToString();
        }
        return string.Empty;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
