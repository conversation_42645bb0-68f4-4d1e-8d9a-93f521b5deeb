# 窗口层级管理测试指南

## 测试目的
验证帧数据监听窗体（FrameDataListenerWindow）的窗口层级管理是否正常工作，确保用户可以自由切换窗体焦点。

## 修复内容

### 1. DialogService修改
- **修改前**：`ShowWindow`方法自动设置`window.Owner = owner`，导致子窗体始终显示在父窗体上方
- **修改后**：
  - 新增`ShowWindow(Window window)`方法，不设置Owner属性，创建真正的独立窗体
  - 保留`ShowWindow(Window window, Window? owner, bool setOwner)`方法，支持需要设置Owner的场景
  - 帧数据监听窗体使用独立窗体模式

### 2. 窗体样式优化
在`FrameDataListenerStyles.xaml`中明确设置：
- `Topmost="False"`：确保窗体不会始终置顶
- `ShowInTaskbar="True"`：允许窗体在任务栏中显示
- `ResizeMode="CanResize"`：允许调整大小
- `WindowStyle="SingleBorderWindow"`：标准窗体样式

## 测试步骤

### 测试1：基本窗口行为
1. 启动应用程序，主窗体应正常显示
2. 点击菜单"监听" → "帧数据监听"
3. **预期结果**：帧数据监听窗体打开，显示在合适位置

### 测试2：窗口焦点切换
1. 确保两个窗体都已打开
2. 点击主窗体的标题栏或内容区域
3. **预期结果**：主窗体应该获得焦点并置于前台
4. 点击帧数据监听窗体的标题栏或内容区域
5. **预期结果**：帧数据监听窗体应该获得焦点并置于前台

### 测试3：任务栏行为
1. 打开两个窗体
2. 查看Windows任务栏
3. **预期结果**：应该看到两个独立的窗体图标
4. 点击任务栏中的不同窗体图标
5. **预期结果**：对应的窗体应该被激活并置于前台

### 测试4：窗体独立性
1. 打开帧数据监听窗体
2. 最小化主窗体
3. **预期结果**：帧数据监听窗体应该保持正常显示，不受主窗体状态影响
4. 关闭主窗体
5. **预期结果**：帧数据监听窗体应该继续正常工作（如果应用程序设计允许）

### 测试5：多实例测试
1. 多次点击"监听" → "帧数据监听"菜单
2. **预期结果**：应该能够打开多个帧数据监听窗体实例
3. 每个窗体都应该独立工作，可以自由切换焦点

## 问题排查

### 如果窗体仍然始终置顶
1. 检查XAML中是否有`Topmost="True"`设置
2. 检查代码中是否有`window.Topmost = true`调用
3. 检查样式文件中的窗体样式设置

### 如果窗体无法获得焦点
1. 检查`ShowInTaskbar`属性是否为True
2. 检查窗体的`WindowState`是否正常
3. 检查是否有其他代码强制设置焦点

### 如果窗体行为异常
1. 检查DialogService中的ShowWindow方法调用
2. 确认使用的是`ShowWindow(window)`而不是`ShowWindow(window, owner, true)`
3. 检查窗体的Owner属性是否为null

## 技术说明

### Owner属性的影响
- **设置Owner**：子窗体始终显示在父窗体上方，跟随父窗体的状态变化
- **不设置Owner**：窗体完全独立，可以自由切换层级，在任务栏中独立显示

### 最佳实践
- **模态对话框**：应该设置Owner，确保正确的模态行为
- **独立工具窗体**：不应该设置Owner，提供更好的用户体验
- **子窗体**：根据具体需求决定是否设置Owner

## 验收标准
✅ 帧数据监听窗体可以正常打开
✅ 用户可以自由切换主窗体和监听窗体的焦点
✅ 两个窗体在任务栏中独立显示
✅ 窗体不会始终置顶
✅ 窗体具有正常的Windows窗口行为
