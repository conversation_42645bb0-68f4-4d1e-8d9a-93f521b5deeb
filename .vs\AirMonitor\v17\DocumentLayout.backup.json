{"Version": 1, "WorkspaceRootPath": "D:\\00 AirMonitor\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\viewmodels\\protocolparseconfigviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\viewmodels\\protocolparseconfigviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\configs\\protocolparseconfigs\\example-commercial-protocol.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\configs\\protocolparseconfigs\\example-commercial-protocol.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\configs\\protocolparseconfigs\\example-module-protocol.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\configs\\protocolparseconfigs\\example-module-protocol.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\controls\\bytepositionselector.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\controls\\bytepositionselector.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 8, "Children": [{"$type": "Bookmark", "Name": "ST:130:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:132:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:131:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:133:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:134:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:135:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d84ee353-0bef-5a41-a649-8f89aca5d84d}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "ProtocolParseConfigViewModel.cs", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\ViewModels\\ProtocolParseConfigViewModel.cs", "RelativeDocumentMoniker": "AirMonitor\\ViewModels\\ProtocolParseConfigViewModel.cs", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\ViewModels\\ProtocolParseConfigViewModel.cs", "RelativeToolTip": "AirMonitor\\ViewModels\\ProtocolParseConfigViewModel.cs", "ViewState": "AgIAABcAAAAAAAAAAAAuwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T07:01:26.295Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "example-module-protocol.json", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\Configs\\ProtocolParseConfigs\\example-module-protocol.json", "RelativeDocumentMoniker": "AirMonitor\\Configs\\ProtocolParseConfigs\\example-module-protocol.json", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\Configs\\ProtocolParseConfigs\\example-module-protocol.json", "RelativeToolTip": "AirMonitor\\Configs\\ProtocolParseConfigs\\example-module-protocol.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-24T07:00:24.037Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "example-commercial-protocol.json", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\Configs\\ProtocolParseConfigs\\example-commercial-protocol.json", "RelativeDocumentMoniker": "AirMonitor\\Configs\\ProtocolParseConfigs\\example-commercial-protocol.json", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\Configs\\ProtocolParseConfigs\\example-commercial-protocol.json", "RelativeToolTip": "AirMonitor\\Configs\\ProtocolParseConfigs\\example-commercial-protocol.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-24T06:59:28.642Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "BytePositionSelector.xaml", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\Controls\\BytePositionSelector.xaml", "RelativeDocumentMoniker": "AirMonitor\\Controls\\BytePositionSelector.xaml", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\Controls\\BytePositionSelector.xaml", "RelativeToolTip": "AirMonitor\\Controls\\BytePositionSelector.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-24T06:55:07.672Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "MainWindow.xaml", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\MainWindow.xaml", "RelativeDocumentMoniker": "AirMonitor\\MainWindow.xaml", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\MainWindow.xaml", "RelativeToolTip": "AirMonitor\\MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-24T06:53:13.957Z"}]}]}]}